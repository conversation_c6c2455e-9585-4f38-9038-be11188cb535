"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent, Progress } from "@repo/ui/components"
import { Eye, Upload, X } from "lucide-react"
import { useTranslations } from "next-intl"
import React, { useState } from "react"
import { toast } from "sonner"
import { CircleLoading } from "./CircleLoading"
import { cva, type VariantProps } from "class-variance-authority"

const imageUploaderVariants = cva("", {
	variants: {
		variant: {
			button: "",
			image: "",
		},
		size: {
			sm: "",
			md: "",
			lg: "",
		},
	},
	compoundVariants: [
		{
			variant: "image",
			size: "sm",
			class: "h-[100px] w-[100px]",
		},
		{
			variant: "image",
			size: "md",
			class: "h-[150px] w-[150px]",
		},
		{
			variant: "image",
			size: "lg",
			class: "h-[200px] w-[200px]",
		},
	],
	defaultVariants: {
		variant: "button",
		size: "md",
	},
})

interface ImageUploaderProps
	extends VariantProps<typeof imageUploaderVariants> {
	value?: string
	onChange?: (url: string) => void
	projectId: string
	accept?: string
}

export default function ImageUploader({
	value,
	onChange,
	projectId,
	accept = "image/*",
	variant = "button",
	size = "md",
}: ImageUploaderProps) {
	const [loading, setLoading] = useState(false)
	const [progress, setProgress] = useState(0)
	const [previewUrl, setPreviewUrl] = useState<string | undefined>(value)
	const t = useTranslations("Editor")

	const { isOpen, onOpen, onClose } = useDisclosure()
	const isVideo = accept.startsWith("video/")

	// 当value属性变化时更新预览URL
	React.useEffect(() => {
		// 只有当value存在时才更新，避免将有值的previewUrl设置为undefined
		if (value) {
			setPreviewUrl(value)
		}
	}, [value])

	function uploadWithProgress(
		url: string,
		file: File,
		onProgress: (percent: number) => void,
	) {
		return new Promise((resolve, reject) => {
			const xhr = new XMLHttpRequest()
			xhr.open("PUT", url, true)

			// 设置Content-Type头
			xhr.setRequestHeader(
				"Content-Type",
				file.type || "application/octet-stream",
			)

			// 打印完整的URL用于调试
			console.log("Uploading to URL:", url)
			console.log("File type:", file.type)

			xhr.upload.onprogress = (event) => {
				if (event.lengthComputable) {
					const percent = (event.loaded / event.total) * 100
					onProgress(Math.round(percent))
				}
			}

			xhr.onload = () => {
				if (xhr.status >= 200 && xhr.status < 300) {
					console.log("Upload successful:", xhr.status)
					resolve(xhr.response)
				} else {
					console.error(
						"Upload failed with status:",
						xhr.status,
						xhr.statusText,
					)
					// 尝试读取响应内容
					try {
						console.error("Response:", xhr.responseText)
					} catch (e) {
						console.error("Could not read response")
					}
					reject(`Status ${xhr.status}: ${xhr.statusText || "Unknown error"}`)
				}
			}

			xhr.onerror = (e) => {
				console.error("XHR error during upload:", e)
				reject(`Network error: ${xhr.statusText || "Unknown error"}`)
			}

			xhr.send(file)
		})
	}

	// 使用useCallback包装上传处理函数，避免不必要的重新创建
	const handleUpload = React.useCallback(
		async (e: React.ChangeEvent<HTMLInputElement>) => {
			const file = e.target.files?.[0]
			if (!file) return

			try {
				setLoading(true)
				setProgress(0)

				// 生成文件路径
				const currentDate = new Date().toISOString().split("T")[0]
				const path = `${projectId}/${currentDate}/${isVideo ? "videos" : "images"}`
				const fileExt = file.name.split(".").pop() || "jpg"
				const fileName = `${path}/${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`

				// 上传文件并监听进度
				// 获取签名URL(避免暴露R2_ACCESS_KEY和R2_SECRET_ACCESS_KEY)
				console.log("Getting signed URL...")
				const res = await fetch(`/api/s3/put-signed-url?key=${fileName}`)

				if (!res.ok) {
					const errorText = await res.text()
					throw new Error(
						`Failed to get signed URL: ${res.status} ${errorText}`,
					)
				}

				const data = await res.json()

				if (!data.url) {
					console.error("API response missing URL:", data)
					throw new Error("API response missing URL")
				}

				console.log("Got signed URL, starting upload...")

				// 直接上传
				await uploadWithProgress(data.url, file, (progress) => {
					setProgress(progress)
				})

				console.log("Upload completed successfully")

				// 构建文件URL
				const fileUrl = `${process.env.R2_PUBLIC_DOMAIN}/${fileName}`

				// 更新预览和值（使用函数式更新确保使用最新状态）
				setPreviewUrl(fileUrl)

				// 只有当URL真正变化时才调用onChange
				if (onChange && fileUrl) {
					onChange(fileUrl)
				}

				toast.success(t("uploadSuccess") || "上传成功")
			} catch (error) {
				console.error("Upload failed:", error)
				const errorMessage =
					error instanceof Error ? error.message : String(error)
				toast.error(t("uploadFailed") || `上传失败: ${errorMessage}`)
			} finally {
				setLoading(false)
				// 使用RAF代替setTimeout，更加浏览器友好
				requestAnimationFrame(() => {
					setTimeout(() => setProgress(0), 1000) // 1秒后重置进度条
				})
			}
		},
		[projectId, isVideo, onChange, t],
	)

	// 使用useCallback包装清除函数
	const handleClear = React.useCallback(() => {
		setPreviewUrl(undefined)
		if (onChange) {
			onChange("")
		}
	}, [onChange])

	return (
		<div className="flex flex-col gap-2">
			<div className="flex items-center gap-2">
				{variant === "button" && (
					<Button
						as="label"
						color="primary"
						variant="flat"
						isLoading={loading}
						startContent={<Upload size={16} />}
					>
						{t(isVideo ? "uploadVideo" : "uploadImage")}
						<input
							type="file"
							className="hidden"
							accept={accept}
							onChange={handleUpload}
							disabled={loading}
							aria-label={t(isVideo ? "uploadVideo" : "uploadImage")}
						/>
					</Button>
				)}

				{variant === "image" && (!previewUrl || previewUrl === "") && (
					<label
						className={`${imageUploaderVariants({ variant, size })} flex flex-col items-center justify-center gap-2 border-2 border-dashed border-default-200 rounded-lg p-4 hover:border-primary hover:text-primary`}
					>
						{loading ? (
							<CircleLoading size={24} />
						) : (
							<>
								<span>
									<Upload size={16} />
								</span>
								<span className="text-sm">
									{t(isVideo ? "uploadVideo" : "uploadImage")}
								</span>
								{size !== "sm" && (
									<span className="text-xs text-muted-foreground">
										{t("supportedFormats") || "支持格式"}: JPG, PNG, WEBP
									</span>
								)}
							</>
						)}
						<input
							type="file"
							className="hidden"
							accept={accept}
							onChange={handleUpload}
							disabled={loading}
							aria-label={t(isVideo ? "uploadVideo" : "uploadImage")}
						/>
					</label>
				)}

				{variant === "button" && progress > 0 && (
					<div className="flex-1 max-w-[200px]">
						<Progress
							size="sm"
							value={progress}
							color="primary"
							showValueLabel={true}
							className="max-w-md"
						/>
					</div>
				)}
			</div>
			{previewUrl && (
				<div className="relative max-w-[400px] max-h-[300px] overflow-hidden rounded-lg bg-default-100 flex items-center justify-center">
					<div className="absolute top-0 right-0 z-20 flex gap-2 p-2 bg-default-100/50 backdrop-blur-sm rounded-bl-lg">
						<Button
							isIconOnly
							color="default"
							variant="flat"
							size="sm"
							onPress={onOpen}
						>
							<Eye size={16} />
						</Button>
						<Button
							isIconOnly
							color="danger"
							variant="light"
							size="sm"
							onPress={handleClear}
						>
							<X size={16} />
						</Button>
					</div>
					{isVideo ? (
						<video
							src={previewUrl}
							controls
							className="max-w-full max-h-[300px] w-auto h-auto"
							muted
						/>
					) : (
						<Image
							src={previewUrl}
							alt="Preview"
							className="max-w-full max-h-[300px] w-auto h-auto object-contain"
							radius="sm"
						/>
					)}
				</div>
			)}
			<Modal
				isOpen={isOpen}
				onClose={onClose}
				size="5xl"
				scrollBehavior="inside"
			>
				<ModalContent>
					<ModalBody className="p-0">
						<div className="relative flex items-center justify-center">
							<div className="absolute top-0 right-0 z-10 p-2 bg-default-100/50 backdrop-blur-sm rounded-bl-lg">
								<Button
									isIconOnly
									color="danger"
									variant="light"
									size="sm"
									onPress={onClose}
								>
									<X size={16} />
								</Button>
							</div>
							{isVideo ? (
								<video
									src={previewUrl}
									controls
									className="w-full h-auto"
									muted
								/>
							) : (
								<Image
									src={previewUrl}
									alt="Preview"
									className="w-full h-auto"
									radius="none"
								/>
							)}
						</div>
					</ModalBody>
				</ModalContent>
			</Modal>
		</div>
	)
}
